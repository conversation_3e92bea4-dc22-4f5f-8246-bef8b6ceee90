#!/usr/bin/env python3
"""
改进的特征提取脚本
使用更好的特征提取方法，符合MMAction2的BMN模型要求
"""

import os
import cv2
import numpy as np
import pandas as pd
from pathlib import Path
import argparse
from tqdm import tqdm

def extract_improved_features(frames, target_dim=400, temporal_dim=100):
    """
    提取改进的视频特征
    
    Args:
        frames: 视频帧列表
        target_dim: 特征维度 (400)
        temporal_dim: 时间维度 (100，符合BMN标准配置)
    
    Returns:
        features: (target_dim, temporal_dim) 的特征矩阵
    """
    if not frames:
        return np.zeros((target_dim, temporal_dim))
    
    num_frames = len(frames)
    
    # 如果帧数不等于temporal_dim，进行采样
    if num_frames != temporal_dim:
        # 均匀采样到temporal_dim个时间步
        indices = np.linspace(0, num_frames - 1, temporal_dim, dtype=int)
        sampled_frames = [frames[i] for i in indices]
    else:
        sampled_frames = frames
    
    features = []
    
    for frame in sampled_frames:
        frame_features = extract_single_frame_features(frame, target_dim)
        features.append(frame_features)
    
    # 转换为 (target_dim, temporal_dim) 格式
    features = np.array(features).T
    
    return features

def extract_single_frame_features(frame, target_dim=400):
    """提取单帧的改进特征"""
    features = []
    
    # 1. 颜色特征 (120维)
    color_features = extract_color_features(frame)
    features.extend(color_features)
    
    # 2. 纹理特征 (140维)
    texture_features = extract_texture_features(frame)
    features.extend(texture_features)
    
    # 3. 形状特征 (80维)
    shape_features = extract_shape_features(frame)
    features.extend(shape_features)
    
    # 4. 运动特征 (60维) - 这里用静态特征代替
    motion_features = extract_motion_like_features(frame)
    features.extend(motion_features)
    
    # 确保特征维度正确
    if len(features) < target_dim:
        # 用特征的统计变体填充
        remaining = target_dim - len(features)
        base_features = features[:remaining] if len(features) >= remaining else features
        for i in range(remaining):
            features.append(base_features[i % len(base_features)] * 0.1)
    
    return features[:target_dim]

def extract_color_features(frame):
    """提取颜色特征"""
    features = []
    
    # RGB直方图
    for channel in range(3):
        hist = cv2.calcHist([frame], [channel], None, [16], [0, 256])
        features.extend(hist.flatten())
    
    # HSV直方图
    hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
    for channel in range(3):
        hist = cv2.calcHist([hsv], [channel], None, [8], [0, 256])
        features.extend(hist.flatten())
    
    # 颜色矩
    for channel in range(3):
        channel_data = frame[:, :, channel].flatten()
        features.extend([
            np.mean(channel_data),
            np.std(channel_data),
            np.var(channel_data)
        ])
    
    return features

def extract_texture_features(frame):
    """提取纹理特征"""
    features = []
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    
    # Sobel梯度
    grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
    grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
    
    # 梯度统计
    for grad in [grad_x, grad_y]:
        grad_flat = grad.flatten()
        features.extend([
            np.mean(grad_flat), np.std(grad_flat), np.var(grad_flat),
            np.percentile(grad_flat, 25), np.percentile(grad_flat, 75),
            np.min(grad_flat), np.max(grad_flat)
        ])
    
    # LBP-like特征
    kernel = np.array([[-1, -1, -1], [-1, 8, -1], [-1, -1, -1]])
    lbp_response = cv2.filter2D(gray, cv2.CV_64F, kernel)
    lbp_flat = lbp_response.flatten()
    features.extend([
        np.mean(lbp_flat), np.std(lbp_flat), np.var(lbp_flat),
        np.percentile(lbp_flat, 25), np.percentile(lbp_flat, 75)
    ])
    
    # 多尺度特征
    for ksize in [5, 7]:
        grad_x_multi = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=ksize)
        grad_y_multi = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=ksize)
        
        for grad in [grad_x_multi, grad_y_multi]:
            grad_flat = grad.flatten()
            features.extend([
                np.mean(grad_flat), np.std(grad_flat),
                np.percentile(grad_flat, 50)
            ])
    
    return features

def extract_shape_features(frame):
    """提取形状特征"""
    features = []
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    
    # 边缘检测
    edges = cv2.Canny(gray, 50, 150)
    edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
    features.append(edge_density)
    
    # 轮廓特征
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if contours:
        # 轮廓统计
        areas = [cv2.contourArea(c) for c in contours]
        perimeters = [cv2.arcLength(c, True) for c in contours]
        
        features.extend([
            len(contours),
            np.mean(areas) if areas else 0,
            np.std(areas) if areas else 0,
            np.mean(perimeters) if perimeters else 0,
            np.std(perimeters) if perimeters else 0,
            max(areas) if areas else 0,
            min(areas) if areas else 0
        ])
    else:
        features.extend([0] * 7)
    
    # 多阈值边缘特征
    for thresh in [30, 100, 200]:
        edges_thresh = cv2.Canny(gray, thresh, thresh * 2)
        density = np.sum(edges_thresh > 0) / (edges_thresh.shape[0] * edges_thresh.shape[1])
        features.append(density)
    
    return features

def extract_motion_like_features(frame):
    """提取运动相关特征（用静态特征模拟）"""
    features = []
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    
    # 光流相关的静态特征
    # 使用图像的局部变化来模拟运动特征
    
    # 分块统计
    h, w = gray.shape
    block_h, block_w = h // 4, w // 4
    
    for i in range(4):
        for j in range(4):
            block = gray[i*block_h:(i+1)*block_h, j*block_w:(j+1)*block_w]
            if block.size > 0:
                features.extend([
                    np.mean(block),
                    np.std(block)
                ])
    
    return features

def process_video_with_improved_features(video_path, output_dir, temporal_dim=100):
    """使用改进方法处理单个视频"""
    print(f"处理视频: {video_path}")
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"无法打开视频: {video_path}")
        return None
    
    # 读取所有帧
    frames = []
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        frames.append(frame)
    cap.release()
    
    if not frames:
        print(f"视频无帧: {video_path}")
        return None
    
    # 提取特征
    features = extract_improved_features(frames, target_dim=400, temporal_dim=temporal_dim)
    
    # 保存特征
    video_name = Path(video_path).stem
    feature_path = os.path.join(output_dir, f"{video_name}.csv")
    
    # 保存为MMAction2期望的格式
    # 每行是特征维度，每列是时间步，有header
    df = pd.DataFrame(features)
    header = [f"t_{i}" for i in range(features.shape[1])]
    df.columns = header
    df.to_csv(feature_path, header=True, index=False)
    
    print(f"特征已保存: {feature_path}")
    print(f"特征形状: {features.shape}")
    
    return {
        'video_name': video_name,
        'feature_path': feature_path,
        'feature_shape': features.shape,
        'duration_frame': len(frames),
        'feature_frame': temporal_dim
    }

def main():
    parser = argparse.ArgumentParser(description='改进的特征提取')
    parser.add_argument('video_path', help='输入视频路径')
    parser.add_argument('--output-dir', default='./improved_features', help='输出目录')
    parser.add_argument('--temporal-dim', type=int, default=100, help='时间维度')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 处理视频
    result = process_video_with_improved_features(
        args.video_path, 
        args.output_dir, 
        args.temporal_dim
    )
    
    if result:
        print(f"\n特征提取完成!")
        print(f"视频: {result['video_name']}")
        print(f"特征形状: {result['feature_shape']}")
        print(f"保存路径: {result['feature_path']}")

if __name__ == '__main__':
    main()
