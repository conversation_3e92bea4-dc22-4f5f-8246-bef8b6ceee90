#!/usr/bin/env python3
"""
多类别TAD推理脚本
根据预测结果将视频分类到不同的输出文件夹
"""

import os
import json
import shutil
from pathlib import Path

def create_output_directories(base_output_dir):
    """创建输出目录结构"""
    class_dirs = {
        'ok': os.path.join(base_output_dir, 'ok'),
        'nok_electric_defect': os.path.join(base_output_dir, 'nok_electric_defect'),
        'nok_appearance_defect': os.path.join(base_output_dir, 'nok_appearance_defect'),
        'unknown': os.path.join(base_output_dir, 'unknown')
    }
    
    for class_name, class_dir in class_dirs.items():
        os.makedirs(class_dir, exist_ok=True)
        print(f"创建输出目录: {class_dir}")
    
    return class_dirs

def process_inference_results(results_file, video_dir, output_dirs):
    """
    处理推理结果，将视频文件复制到对应的类别文件夹
    
    Args:
        results_file: 推理结果JSON文件路径
        video_dir: 原始视频文件目录
        output_dirs: 输出目录字典
    """
    with open(results_file, 'r') as f:
        results = json.load(f)
    
    # 类别映射
    class_mapping = {
        0: 'nok_appearance_defect',
        1: 'nok_electric_defect', 
        2: 'ok'
    }
    
    for video_name, predictions in results.items():
        # 获取预测的类别（这里需要根据实际的结果格式调整）
        predicted_class_id = get_predicted_class(predictions)
        predicted_class = class_mapping.get(predicted_class_id, 'unknown')
        
        # 找到对应的视频文件
        video_file = find_video_file(video_name, video_dir)
        
        if video_file and os.path.exists(video_file):
            # 复制到对应的输出目录
            output_dir = output_dirs[predicted_class]
            output_file = os.path.join(output_dir, os.path.basename(video_file))
            
            shutil.copy2(video_file, output_file)
            print(f"视频 {video_name} 预测为 {predicted_class}, 已复制到 {output_file}")
        else:
            print(f"警告: 找不到视频文件 {video_name}")

def get_predicted_class(predictions):
    """从预测结果中提取类别ID（需要根据实际格式调整）"""
    # 这里需要根据BMN模型的实际输出格式来实现
    # 示例实现：
    if 'predictions' in predictions:
        return predictions['predictions'][0]['label_id']
    return 0  # 默认返回第一个类别

def find_video_file(video_name, video_dir):
    """在视频目录中查找对应的视频文件"""
    video_dir = Path(video_dir)
    
    # 尝试不同的扩展名
    for ext in ['.mp4', '.avi', '.mov']:
        video_file = video_dir / f"{video_name}{ext}"
        if video_file.exists():
            return str(video_file)
    
    return None

def main():
    # 配置路径
    results_file = "work_dirs/bmn_multiclass_tad/results.json"  # 推理结果文件
    video_dir = "data/segmented_videos_ok"  # 原始视频目录
    base_output_dir = "output/classified_videos"  # 输出基础目录
    
    # 创建输出目录
    output_dirs = create_output_directories(base_output_dir)
    
    # 处理推理结果
    if os.path.exists(results_file):
        process_inference_results(results_file, video_dir, output_dirs)
        print("视频分类完成！")
    else:
        print(f"推理结果文件不存在: {results_file}")
        print("请先运行推理命令:")
        print("python tools/test.py configs/localization/bmn/bmn_2xb4-16x400-20e_multiclass-tad-feature.py work_dirs/bmn_multiclass_tad/latest.pth")

if __name__ == "__main__":
    main()
